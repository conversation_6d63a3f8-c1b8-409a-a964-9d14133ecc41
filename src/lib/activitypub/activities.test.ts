import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processActivityObject } from './activities.js';
import {
  createTestCreateActivity,
  createTestDeleteActivity,
  createTestFollowActivity,
  createTestAcceptActivity,
  createTestBlockActivity,
  createTestAddActivity,
  createTestRemoveActivity
} from '../../test/activitypub-helpers.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../test/activitypub-test-utils';

// Mock dependencies using common utilities
vi.mock('./utils', () => ({
  fetchAndProcessAPObject: vi.fn(),
  isCreateActivity: vi.fn(),
  processActivitySafely: vi.fn().mockReturnValue({ success: true }),
  ActivityProcessors: {
    processCreate: vi.fn(),
    processDelete: vi.fn(),
    processFollow: vi.fn(),
    processAccept: vi.fn(),
    processBlock: vi.fn(),
    processAdd: vi.fn(),
    processRemove: vi.fn()
  },
  getActivitySummary: vi.fn().mockReturnValue({ type: 'Create' }),
  FetchError: class FetchError extends Error {
    constructor(message: string, public code?: string, public context?: any) {
      super(message);
    }
  },
  withErrorRecovery: vi.fn(),
  processActivityObjects: vi.fn()
}));

vi.mock('./posts', () => ({
  importPost: vi.fn(),
  deletePost: vi.fn(),
  getVisibilityFromPost: vi.fn()
}));

vi.mock('./follows', () => ({
  processFollow: vi.fn(),
  processAccept: vi.fn()
}));

vi.mock('./blocks', () => ({
  processBlock: vi.fn()
}));

vi.mock('./collections', () => ({
  processAdd: vi.fn(),
  processRemove: vi.fn()
}));

vi.mock('./utils/types', () => ({
  getObjectType: vi.fn()
}));

vi.mock('./utils/activities', () => ({
  ActivityProcessors: {
    processCreate: vi.fn(),
    processDelete: vi.fn(),
    processFollow: vi.fn(),
    processAccept: vi.fn(),
    processBlock: vi.fn(),
    processAdd: vi.fn(),
    processRemove: vi.fn()
  },
  processActivitySafely: vi.fn(),
  getActivitySummary: vi.fn()
}));

import { ActivityProcessors, processActivitySafely, getActivitySummary } from './utils/activities';
import { importPost } from './posts';
import { processFollow, processAccept } from './follows';
import { processBlock } from './blocks';
import { processAdd, processRemove } from './collections';
import { getObjectType } from './utils/types';

describe('Activity Processing', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('processActivityObject', () => {
    it('should process Create activity', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Create', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(ActivityProcessors.processCreate).mockResolvedValue({
        processed: 1,
        failed: 0,
        errors: []
      });

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(ActivityProcessors.processCreate).toHaveBeenCalledWith(
        activity,
        expect.any(Function)
      );
    });

    it('should process Delete activity', async () => {
      // Arrange
      const activity = createTestDeleteActivity('https://example.com/notes/1');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Delete', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(ActivityProcessors.processDelete).mockResolvedValue({
        processed: 1,
        failed: 0,
        errors: []
      });

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(ActivityProcessors.processDelete).toHaveBeenCalledWith(
        activity,
        expect.any(Function)
      );
    });

    it('should process Follow activity', async () => {
      // Arrange
      const activity = createTestFollowActivity('https://example.com/users/target');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Follow', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(processFollow).mockResolvedValue(true);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(processFollow).toHaveBeenCalledWith(activity);
    });

    it('should process Accept activity', async () => {
      // Arrange
      const activity = createTestAcceptActivity('https://example.com/activities/follow-1');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Accept', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(processAccept).mockResolvedValue(true);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(processAccept).toHaveBeenCalledWith(activity);
    });

    it('should process Block activity', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Block', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(processBlock).mockResolvedValue(true);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(processBlock).toHaveBeenCalledWith(activity);
    });

    it('should process Add activity', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Add', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(processAdd).mockResolvedValue(true);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(processAdd).toHaveBeenCalledWith(activity);
    });

    it('should process Remove activity', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Remove', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(processRemove).mockResolvedValue(true);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(getActivitySummary).toHaveBeenCalledWith(activity);
      expect(processRemove).toHaveBeenCalledWith(activity);
    });

    it('should handle unsupported activity types gracefully', async () => {
      // Arrange
      const activity = { type: 'UnsupportedType' } as any;
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'UnsupportedType', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(consoleSpy).toHaveBeenCalledWith('Unsupported activity type: UnsupportedType');

      consoleSpy.mockRestore();
    });

    it('should handle Create activity processing with importPost', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Create', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });

      let capturedImportFunction: Function;
      vi.mocked(ActivityProcessors.processCreate).mockImplementation(async (act, importFn) => {
        capturedImportFunction = importFn;
        return { processed: 1, failed: 0, errors: [] };
      });

      vi.mocked(importPost).mockResolvedValue({} as any);

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(ActivityProcessors.processCreate).toHaveBeenCalled();

      // Test the import function that was passed
      await capturedImportFunction!('https://example.com/notes/1');
      expect(importPost).toHaveBeenCalledWith('https://example.com/notes/1');
    });

    it('should handle import errors in Create activity processing', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Create', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });

      let capturedImportFunction: Function;
      vi.mocked(ActivityProcessors.processCreate).mockImplementation(async (act, importFn) => {
        capturedImportFunction = importFn;
        return { processed: 0, failed: 1, errors: ['Import failed'] };
      });

      vi.mocked(importPost).mockRejectedValue(new Error('Import failed'));

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(ActivityProcessors.processCreate).toHaveBeenCalled();

      // Test that the import function throws the error
      await expect(capturedImportFunction!('https://example.com/notes/1')).rejects.toThrow('Import failed');
    });

    it('should log warnings for failed processing', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Create', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(ActivityProcessors.processCreate).mockResolvedValue({
        processed: 1,
        failed: 1,
        errors: ['Some error occurred']
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Create activity processing completed with 1 failures:',
        ['Some error occurred']
      );

      consoleSpy.mockRestore();
    });

    it('should log success messages', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      vi.mocked(getActivitySummary).mockReturnValue({ type: 'Create', actor: 'test-actor', objectCount: 1 });
      vi.mocked(processActivitySafely).mockReturnValue({ success: true, activity });
      vi.mocked(ActivityProcessors.processCreate).mockResolvedValue({
        processed: 2,
        failed: 0,
        errors: []
      });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      const result = await processActivityObject(activity);

      // Assert
      expect(result).toBe(activity);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Successfully processed 2 objects from Create activity'
      );

      consoleSpy.mockRestore();
    });
  });
});
